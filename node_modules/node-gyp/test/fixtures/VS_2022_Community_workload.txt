[{"path": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community", "version": "17.4.33213.308", "packages": ["Microsoft.VisualStudio.Product.Community", "Microsoft.VisualStudio.PackageGroup.LiveShare.VSCore", "Microsoft.VisualStudio.LiveShare.VSCore", "Microsoft.VisualStudio.Workload.NativeDesktop", "Microsoft.VisualStudio.Component.VC.ASAN", "Microsoft.VisualCpp.ASAN.X86", "Microsoft.VC.**********.ASAN.X86.base", "Microsoft.VC.**********.ASAN.X64.base", "Microsoft.VC.**********.ASAN.Headers.base", "Microsoft.VisualStudio.VC.IDE.Project.Factories", "Microsoft.VisualStudio.Component.VC.TestAdapterForGoogleTest", "Microsoft.VisualStudio.VC.Ide.TestAdapterForGoogleTest", "Microsoft.VisualStudio.Component.VC.TestAdapterForBoostTest", "Microsoft.VisualStudio.VC.Ide.TestAdapterForBoostTest", "Microsoft.VisualStudio.Component.VC.CMake.Project", "Microsoft.VisualStudio.ComponentGroup.WebToolsExtensions.CMake", "Microsoft.VisualStudio.VC.CMake", "Microsoft.VisualStudio.VC.CMake.Project", "Microsoft.VisualStudio.VC.CMake.Client", "Microsoft.VisualStudio.VC.ExternalBuildFramework", "Microsoft.VisualStudio.Component.VC.DiagnosticTools", "Microsoft.VisualStudio.ComponentGroup.NativeDesktop.Core", "Microsoft.VisualStudio.PackageGroup.TestTools.Native", "Microsoft.VisualStudio.Component.VC.Redist.14.Latest", "Microsoft.VisualStudio.VC.Templates.UnitTest", "Microsoft.VisualStudio.VC.UnitTest.Desktop.Build.Core", "Microsoft.VisualStudio.TestTools.TestPlatform.V1.CPP", "Microsoft.VisualStudio.VC.Templates.UnitTest.Resources", "Microsoft.VisualStudio.VC.Templates.Desktop", "Microsoft.VisualStudio.Component.Graphics", "Microsoft.VisualStudio.Graphics.Viewers", "Microsoft.VisualStudio.Graphics.Viewers.Resources", "Microsoft.VisualStudio.Component.VC.ATL.ARM64", "Microsoft.VisualCpp.ATL.ARM64", "Microsoft.VC.**********.ATL.ARM64.base", "Microsoft.VisualStudio.Component.VC.ATL", "Microsoft.VisualStudio.VC.Ide.ATL", "Microsoft.VisualStudio.VC.Ide.ATL.Resources", "Microsoft.VisualCpp.ATL.X86", "Microsoft.VC.**********.ATL.X86.base", "Microsoft.VisualCpp.ATL.X64", "Microsoft.VC.**********.ATL.X64.base", "Microsoft.VC.**********.Props.ATLMFC", "Microsoft.VisualCpp.ATL.Source", "Microsoft.VC.**********.ATL.Source.base", "Microsoft.VisualCpp.ATL.Headers", "Microsoft.VC.**********.ATL.Headers.base", "Microsoft.VC.**********.Servicing.ATL", "Microsoft.VisualStudio.Component.VC.Tools.ARM64", "Microsoft.VisualStudio.VC.MSBuild.v170.ARM64.v143", "Microsoft.VisualStudio.VC.MSBuild.v170.ARM64", "Microsoft.VS.VC.vcvars.arm64.Shortcuts", "Microsoft.VisualCpp.CA.Ext.Hostx64.TargetARM64", "Microsoft.VC.**********.CA.Ext.Hostx64.TargetARM64.base", "Microsoft.VC.**********.CA.Ext.Hostx64.TargetARM64.Res.base", "Microsoft.VisualCpp.CA.Ext.Hostx86.TargetARM64", "Microsoft.VC.**********.CA.Ext.Hostx86.TargetARM64.base", "Microsoft.VC.**********.CA.Ext.Hostx86.TargetARM64.Res.base", "Microsoft.VisualCpp.CA.Ext.HostARM64.TargetARM64", "Microsoft.VC.**********.CA.Ext.HostARM64.TargetARM64.base", "Microsoft.VC.**********.CA.Ext.HostARM64.TargetARM64.Res.base", "Microsoft.VisualCpp.Tools.Hostx86.Targetarm64", "Microsoft.VC.**********.Tools.Hostx86.Targetarm64.base", "Microsoft.VC.**********.Tools.HostX86.TargetARM64.Res.base", "Microsoft.VisualCpp.Tools.HostARM64.TargetARM64", "Microsoft.VC.**********.Tools.HostARM64.TargetARM64.base", "Microsoft.VC.**********.Tools.HostARM64.TargetARM64.Res.base", "Microsoft.VisualCpp.CRT.Redist.ARM64.OneCore.Desktop", "Microsoft.VC.**********.CRT.Redist.ARM64.OneCore.Desktop.base", "Microsoft.VisualCpp.CRT.Redist.ARM64", "Microsoft.VC.**********.CRT.Redist.ARM64.base", "Microsoft.VisualCpp.CRT.ARM64.OneCore.Desktop", "Microsoft.VC.**********.CRT.ARM64.OneCore.Desktop.base", "Microsoft.VC.**********.CRT.ARM64.OneCore.Desktop.debug.base", "Microsoft.VisualCpp.CRT.ARM64.Store", "Microsoft.VC.**********.CRT.ARM64.Store.base", "Microsoft.VisualCpp.CRT.ARM64.Desktop", "Microsoft.VC.**********.CRT.ARM64.Desktop.base", "Microsoft.VC.**********.CRT.ARM64.Desktop.debug.base", "Microsoft.VisualStudio.PackageGroup.VC.Tools.x64.ARM64", "Microsoft.VisualCpp.Tools.Core", "Microsoft.VisualCpp.PGO.ARM64", "Microsoft.VC.**********.PGO.ARM64.base", "Microsoft.VisualCpp.Premium.Tools.Hostx86.Targetarm64", "Microsoft.VC.**********.Premium.Tools.Hostx86.Targetarm64.base", "Microsoft.VC.**********.Prem.HostX86.TargetARM64.Res.base", "Microsoft.VisualCpp.Premium.Tools.HostX64.TargetARM64", "Microsoft.VC.**********.Premium.Tools.HostX64.TargetARM64.base", "Microsoft.VC.**********.Prem.HostX64.TargetARM64.Res.base", "Microsoft.VisualCpp.Premium.Tools.ARM64.Base", "Microsoft.VC.**********.Premium.Tools.ARM64.Base.base", "Microsoft.VisualCpp.Tools.HostX64.TargetARM64", "Microsoft.VC.**********.Tools.HostX64.TargetARM64.base", "Microsoft.VC.**********.Props.ARM64", "Microsoft.VC.**********.Tools.HostX64.TargetARM64.Res.base", "Microsoft.VisualStudio.Component.VC.Tools.ARM64EC", "Microsoft.VisualStudio.Component.Windows11SDK.22621", "Win11SDK_10.0.22621", "Microsoft.VisualStudio.VC.MSBuild.v170.ARM64EC.v143", "Microsoft.VisualStudio.VC.MSBuild.v170.ARM64EC", "Microsoft.VisualCpp.CRT.ARM64EC.Store", "Microsoft.VC.**********.CRT.ARM64EC.Store.base", "Microsoft.VisualStudio.Component.VC.Tools.x86.x64", "Microsoft.VisualCpp.CodeAnalysis.Extensions", "Microsoft.VisualCpp.CA.Ext.HostARM64.Targetx64", "Microsoft.VC.**********.CA.Ext.HostARM64.Targetx64.base", "Microsoft.VC.**********.CA.Ext.HostARM64.Targetx64.Res.base", "Microsoft.VisualCpp.CA.Ext.HostARM64.Targetx86", "Microsoft.VC.**********.CA.Ext.HostARM64.Targetx86.base", "Microsoft.VC.**********.CA.Ext.HostARM64.Targetx86.Res.base", "Microsoft.VisualCpp.CA.Ext.Hostx86.Targetx64", "Microsoft.VC.**********.CA.Ext.Hostx86.Targetx64.base", "Microsoft.VC.**********.CA.Ext.Hostx86.Targetx64.Res.base", "Microsoft.VisualCpp.CA.Ext.Hostx86.Targetx86", "Microsoft.VC.**********.CA.Ext.Hostx86.Targetx86.base", "Microsoft.VC.**********.CA.Ext.Hostx86.Targetx86.Res.base", "Microsoft.VisualCpp.CA.Ext.Hostx64.Targetx64", "Microsoft.VC.**********.CA.Ext.Hostx64.Targetx64.base", "Microsoft.VC.**********.CA.Ext.Hostx64.Targetx64.Res.base", "Microsoft.VisualCpp.CA.Ext.Hostx64.Targetx86", "Microsoft.VC.**********.CA.Ext.Hostx64.Targetx86.base", "Microsoft.VC.**********.Servicing.CAExtensions", "Microsoft.VC.**********.CA.Ext.Hostx64.Targetx86.Res.base", "Microsoft.VisualCpp.Tools.HostX64.TargetX86", "Microsoft.VC.**********.Tools.HostX64.TargetX86.base", "Microsoft.VC.**********.Tools.HostX64.TargetX86.Res.base", "Microsoft.VisualCpp.Tools.HostX64.TargetX64", "Microsoft.VC.**********.Tools.HostX64.TargetX64.base", "Microsoft.VC.**********.Tools.HostX64.TargetX64.Res.base", "Microsoft.VisualCpp.Tools.HostARM64.TargetX86", "Microsoft.VC.**********.Tools.HostARM64.TargetX86.base", "Microsoft.VisualCpp.RuntimeDebug.14", "Microsoft.VC.**********.Tools.HostARM64.TargetX86.Res.base", "Microsoft.VisualCpp.Tools.HostARM64.TargetX64", "Microsoft.VC.**********.Tools.HostARM64.TargetX64.base", "Microsoft.VisualCpp.RuntimeDebug.14.ARM64", "Microsoft.VisualCpp.Redist.14.Latest", "Microsoft.VisualCpp.Redist.14.Latest", "Microsoft.VC.**********.Tools.HostARM64.Targetx64.Res.base", "Microsoft.VisualCpp.Premium.Tools.HostX86.TargetX64", "Microsoft.VC.**********.Premium.Tools.HostX86.TargetX64.base", "Microsoft.VC.**********.Prem.Hostx86.Targetx64.Res.base", "Microsoft.VisualCpp.Premium.Tools.HostX86.TargetX86", "Microsoft.VC.**********.Premium.Tools.HostX86.TargetX86.base", "Microsoft.VC.**********.Prem.HostX86.TargetX86.Res.base", "Microsoft.VisualCpp.Premium.Tools.HostARM64.TargetX86", "Microsoft.VC.**********.Premium.Tools.HostARM64.TargetX86.base", "Microsoft.VC.**********.Prem.HostARM64.TargetX86.Res.base", "Microsoft.VisualCpp.Premium.Tools.HostARM64.TargetX64", "Microsoft.VC.**********.Premium.Tools.HostARM64.TargetX64.base", "Microsoft.VC.**********.Prem.HostARM64.Targetx64.Res.base", "Microsoft.VisualCpp.Premium.Tools.HostX64.TargetX86", "Microsoft.VC.**********.Premium.Tools.HostX64.TargetX86.base", "Microsoft.VC.**********.Prem.HostX64.TargetX86.Res.base", "Microsoft.VisualCpp.Premium.Tools.HostX64.TargetX64", "Microsoft.VC.**********.Premium.Tools.HostX64.TargetX64.base", "Microsoft.VC.**********.Prem.HostX64.TargetX64.Res.base", "Microsoft.VisualCpp.PGO.X86", "Microsoft.VC.**********.PGO.X86.base", "Microsoft.VisualCpp.PGO.X64", "Microsoft.VC.**********.PGO.X64.base", "Microsoft.VisualCpp.PGO.Headers", "Microsoft.VC.**********.PGO.Headers.base", "Microsoft.VisualCpp.CRT.x86.Store", "Microsoft.VC.**********.CRT.x86.Store.base", "Microsoft.VisualCpp.CRT.x86.OneCore.Desktop", "Microsoft.VC.**********.CRT.x86.OneCore.Desktop.base", "Microsoft.VisualCpp.CRT.x64.Store", "Microsoft.VC.**********.CRT.x64.Store.base", "Microsoft.VisualCpp.CRT.x64.OneCore.Desktop", "Microsoft.VC.**********.CRT.x64.OneCore.Desktop.base", "Microsoft.VisualCpp.CRT.Redist.x86.OneCore.Desktop", "Microsoft.VC.**********.CRT.Redist.x86.OneCore.Desktop.base", "Microsoft.VisualCpp.CRT.Redist.x64.OneCore.Desktop", "Microsoft.VC.**********.CRT.Redist.x64.OneCore.Desktop.base", "Microsoft.VisualStudio.PackageGroup.VC.Tools.x86", "Microsoft.VisualCpp.Tools.Hostx86.Targetx64.Res", "Microsoft.VisualCpp.Tools.HostX86.TargetX64", "Microsoft.VC.**********.Tools.HostX86.TargetX64.base", "Microsoft.VC.**********.Props.x64", "Microsoft.VC.**********.Tools.Hostx86.Targetx64.Res.base", "Microsoft.VisualCpp.Tools.HostX86.TargetX86.Res", "Microsoft.VisualCpp.Tools.HostX86.TargetX86", "Microsoft.VC.**********.Tools.HostX86.TargetX86.base", "Microsoft.VC.**********.Servicing.Compilers", "Microsoft.VC.**********.Props.x86", "Microsoft.VC.**********.Props", "Microsoft.VC.**********.Tools.HostX86.TargetX86.Res.base", "Microsoft.VisualCpp.Tools.Core.Resources", "Microsoft.VisualCpp.Tools.Core.x86", "Microsoft.VC.**********.Tools.Core.Props", "Microsoft.VisualCpp.DIA.SDK", "Microsoft.VisualCpp.Servicing.DIASDK", "Microsoft.VisualCpp.CRT.x86.Desktop", "Microsoft.VC.**********.CRT.x86.Desktop.base", "Microsoft.VisualCpp.CRT.x64.Desktop", "Microsoft.VC.**********.CRT.x64.Desktop.base", "Microsoft.VisualCpp.CRT.Source", "Microsoft.VC.**********.CRT.Source.base", "Microsoft.VisualCpp.CRT.Redist.X86", "Microsoft.VC.**********.CRT.Redist.X86.base", "Microsoft.VisualCpp.CRT.Redist.X64", "Microsoft.VisualCpp.CRT.Redist.Resources", "Microsoft.VC.**********.CRT.Redist.X64.base", "Microsoft.VisualCpp.CRT.Headers", "Microsoft.VC.**********.CRT.Headers.base", "Microsoft.VC.**********.Servicing.CrtHeaders", "Microsoft.VC.**********.Servicing", "Microsoft.VisualStudio.Component.VC.CoreIde", "Microsoft.VisualStudio.VC.Ide.Pro", "Microsoft.VisualStudio.VC.Ide.Pro.Resources", "Microsoft.VisualStudio.VC.Templates.General", "Microsoft.VisualStudio.VC.Templates.General.Resources", "Microsoft.VisualStudio.VC.Items.Pro", "Microsoft.VisualStudio.PackageGroup.VC.CoreIDE.Reduced", "Microsoft.VisualStudio.VC.Ide.x64", "Microsoft.VisualStudio.PackageGroup.VC.CoreIDE.Express", "Microsoft.VisualStudio.VC.vcvars", "Microsoft.VS.VC.vcvars.x86.Shortcuts", "Microsoft.VS.VC.vcvars.x64.Shortcuts", "Microsoft.VS.VC.vcvars.arm64_x64.Shortcuts", "Microsoft.VisualStudio.VC.MSBuild.v170.X64.v143", "Microsoft.VisualStudio.VC.MSBuild.v170.X64", "Microsoft.VisualStudio.VC.MSBuild.v170.ARM.v143", "Microsoft.VisualStudio.VC.MSBuild.v170.ARM", "Microsoft.VisualStudio.VC.MSBuild.v170.x86.v143", "Microsoft.VisualStudio.VC.MSBuild.v170.X86", "Microsoft.VisualStudio.VC.MSBuild.v170.Base", "Microsoft.VisualStudio.VC.MSBuild.v170.Base.Resources", "Microsoft.VisualStudio.VC.Ide.WinXPlus", "Microsoft.VisualStudio.VC.Ide.Dskx", "Microsoft.VisualStudio.VC.Ide.Dskx.Resources", "Microsoft.VisualStudio.VC.Ide.Base", "Microsoft.VisualStudio.VC.Ide.LanguageService", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.Scripts", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.PythonDistro", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.10", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.9", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.8", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.7", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.6", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.5", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.4", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.3", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.2", "Microsoft.VisualStudio.VC.Ide.SecurityIssueAnalysis.3rdPartyLibs.1", "Microsoft.VisualStudio.VC.Ide.VCPkgDatabase", "Microsoft.VisualStudio.VC.Ide.Core", "Microsoft.VisualStudio.VC.Ide.ProjectSystem", "Microsoft.VisualStudio.VC.Ide.ProjectSystem.Resources", "Microsoft.VisualStudio.VC.Ide.Core.VCProjectEngine", "Microsoft.VisualStudio.VC.Ide.Core.VCProjectEngine.Resources", "Microsoft.VisualStudio.VC.Ide.LanguageService.Resources", "Microsoft.VisualStudio.VC.Llvm.Base", "Microsoft.VisualStudio.VC.Ide.Base.Resources", "Microsoft.Net.PackageGroup.4.8.1.Redist", "Microsoft.VisualStudio.Component.IntelliCode", "Microsoft.VisualStudio.IntelliCode.CSharp", "Microsoft.VisualStudio.IntelliCode", "Component.Microsoft.VisualStudio.LiveShare.2022", "Microsoft.VisualStudio.Component.Debugger.JustInTime", "Microsoft.VisualStudio.Debugger.ImmersiveActivateHelper.Msi", "Microsoft.VisualStudio.Debugger.JustInTime", "Microsoft.VisualStudio.Debugger.JustInTime.Msi", "Microsoft.VisualStudio.LiveShare.2022", "Microsoft.Icecap.Analysis", "Microsoft.Icecap.Analysis.Resources", "Microsoft.Icecap.Analysis.Resources.Targeted", "Microsoft.Icecap.Collection.Msi", "Microsoft.Icecap.Collection.Msi.Targeted", "Microsoft.Icecap.Collection.Msi.Resources", "Microsoft.Icecap.Collection.Msi.Resources.Targeted", "Microsoft.DiagnosticsHub.Instrumentation", "Microsoft.DiagnosticsHub.Instrumentation.Targeted", "Microsoft.DiagnosticsHub.CpuSampling", "Microsoft.DiagnosticsHub.CpuSampling.Targeted", "Microsoft.PackageGroup.DiagnosticsHub.Platform", "Microsoft.VisualStudio.InstrumentationEngine.ARM64", "Microsoft.VisualStudio.InstrumentationEngine", "Microsoft.DiagnosticsHub.Runtime.ExternalDependencies", "SQLiteCore", "SQLiteCore.Targeted", "Microsoft.DiagnosticsHub.Runtime.ExternalDependencies.Targeted", "Microsoft.DiagnosticsHub.Runtime", "Microsoft.DiagnosticsHub.Runtime.Targeted", "Microsoft.DiagnosticsHub.Collection.ExternalDependencies.arm64", "Microsoft.DiagnosticsHub.Collection", "Microsoft.DiagnosticsHub.Collection.Service", "Microsoft.VisualStudio.VC.Ide.MDD", "Microsoft.VisualStudio.VC.Ide.Linux.ConnectionManager", "Microsoft.VisualStudio.VisualC.Utilities", "Microsoft.VisualStudio.VisualC.Utilities.Resources", "Microsoft.VisualStudio.VC.Ide.Linux.ConnectionManager.Resources", "Microsoft.VisualStudio.VC.Ide.ResourceEditor", "Microsoft.VisualStudio.VC.Ide.ResourceEditor.Resources", "Microsoft.VisualStudio.PackageGroup.TestTools.Core", "Microsoft.VisualStudio.PackageGroup.TestTools.TestPlatform.V2.CLI", "Microsoft.VisualStudio.TestTools.TestPlatform.V2.CLI", "Microsoft.VisualStudio.TestTools.Pex.Common", "Microsoft.VisualStudio.PackageGroup.TestTools.TestPlatform.V1.CLI", "Microsoft.VisualStudio.PackageGroup.TestTools.TestPlatform.Legacy", "Microsoft.VisualStudio.PackageGroup.MinShell.Interop", "Microsoft.VisualStudio.TestTools.TP.Legacy.Tips.Msi", "Microsoft.VisualStudio.TestTools.TP.Legacy.Tips.Common", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.Tips", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.Tips.Resources", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.TestSettings", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.Professional", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.Common", "Microsoft.VisualStudio.TestTools.TP.Legacy.Common.Res", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.Core", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.Core.Resources", "Microsoft.VisualStudio.TestTools.TestPlatform.Legacy.Agent", "Microsoft.VisualStudio.PackageGroup.TestTools.TestPlatform.IDE", "Microsoft.VisualStudio.Cache.Service", "Microsoft.VisualStudio.TestTools.TestWIExtension", "Microsoft.VisualStudio.TestTools.TestPlatform.V1.CLI", "Microsoft.VisualStudio.TestTools.TestPlatform.IDE", "Microsoft.VisualStudio.PackageGroup.TestTools.CodeCoverage", "Microsoft.VisualStudio.PackageGroup.TestTools.DataCollectors", "Microsoft.VisualStudio.Component.NuGet", "Microsoft.CredentialProvider", "Microsoft.VisualStudio.NuGet.Licenses", "Microsoft.VisualStudio.Component.TextTemplating", "Microsoft.VisualStudio.TextTemplating.MSBuild", "Microsoft.VisualStudio.TextTemplating.Integration", "Microsoft.VisualStudio.TextTemplating.Core", "Microsoft.VisualStudio.TextTemplating.Integration.Resources", "Microsoft.VisualCpp.CRT.ClickOnce.Msi", "Microsoft.VisualStudio.Component.Roslyn.LanguageServices", "Microsoft.VisualStudio.InteractiveWindow", "Microsoft.DiaSymReader.Native", "Microsoft.VisualCpp.Redist.14", "Microsoft.VisualCpp.Redist.14", "Microsoft.VisualCpp.Servicing.Redist", "Microsoft.VisualStudio.PackageGroup.StaticAnalysis", "Microsoft.VisualStudio.StaticAnalysis.IDE", "Microsoft.VisualStudio.StaticAnalysis.IDE.Resources", "Microsoft.VisualStudio.StaticAnalysis.FxCop.Resources", "Microsoft.VisualStudio.StaticAnalysis.auxil", "Microsoft.VisualStudio.StaticAnalysis.auxil.Resources", "Roslyn.VisualStudio.Setup.ServiceHub", "Microsoft.Component.MSBuild", "Microsoft.NuGet.Build.Tasks.Setup", "Microsoft.VisualStudio.Component.Roslyn.Compiler", "Microsoft.CodeAnalysis.Compilers", "Microsoft.VisualStudio.Component.JavaScript.TypeScript", "Microsoft.VisualStudio.JavaScript.ProjectSystem", "Microsoft.VisualStudio.ComponentGroup.WebToolsExtensions", "Microsoft.VisualStudio.ProTools", "sqlsysclrtypes", "SQLCommon", "Microsoft.VisualStudio.ProTools.Resources", "Microsoft.VisualStudio.Web.Scaffolding", "Microsoft.VisualStudio.WebToolsExtensions", "Microsoft.VisualStudio.ConnectedServices.Core", "Microsoft.VisualStudio.WebTools", "Microsoft.VisualStudio.WebToolsExtensions.MSBuild", "Microsoft.VisualStudio.WebTools.Resources", "Microsoft.VisualStudio.WebTools.WSP.FSA", "Microsoft.VisualStudio.WebTools.WSP.FSA.Resources", "Microsoft.VisualStudio.PackageGroup.Debugger.Script", "Microsoft.VisualStudio.Component.TypeScript.TSServer", "Microsoft.VisualStudio.Package.TypeScript.TSServer", "Microsoft.VisualStudio.PackageGroup.JavaScript.Language", "Microsoft.VisualStudio.Package.NodeJs", "TypeScript.Build", "TypeScript.LanguageService", "TypeScript.Tools", "Microsoft.VisualStudio.PackageGroup.Community", "Microsoft.VisualStudio.Community.VB.x86", "Microsoft.VisualStudio.Community.VB.x64", "Microsoft.VisualStudio.PackageGroup.Core", "Microsoft.VisualStudio.CodeSense.Community", "Microsoft.VisualStudio.TestTools.TeamFoundationClient", "Microsoft.VisualStudio.PackageGroup.Debugger.Core", "Microsoft.VisualStudio.Debugger.BrokeredServices", "Microsoft.VisualStudio.Debugger.VSCodeDebuggerHost", "Microsoft.VisualStudio.Debugger.AzureAttach", "Microsoft.VisualStudio.Web.Azure.Common", "Microsoft.WebTools.Shared", "Microsoft.WebTools.DotNet.Core.ItemTemplates", "Microsoft.VisualStudio.PackageGroup.Debugger.TimeTravel.Replay", "Microsoft.VisualStudio.VC.Ide.Debugger", "Microsoft.VisualStudio.VC.Ide.Debugger.Concord", "Microsoft.VisualStudio.VC.Ide.Debugger.Concord.Resources", "Microsoft.VisualStudio.VC.Ide.Debugger.Resources", "Microsoft.VisualStudio.VC.Ide.Common", "Microsoft.VisualStudio.VC.Ide.Common.Resources", "Microsoft.VisualStudio.Debugger.CollectionAgents", "Microsoft.VisualStudio.Debugger.Parallel", "Microsoft.VisualStudio.Debugger.Parallel.Resources", "Microsoft.VisualStudio.Debugger.Managed", "Microsoft.CodeAnalysis.ExpressionEvaluator", "Microsoft.CodeAnalysis.VisualStudio.Setup", "Microsoft.VisualStudio.Debugger.Concord.Managed", "Microsoft.VisualStudio.Debugger.Concord.Managed.Resources", "Microsoft.VisualStudio.Debugger.Managed.Resources", "Microsoft.VisualStudio.Debugger.TargetComposition", "Microsoft.VisualStudio.Debugger.TargetComposition.Remote.arm64", "Microsoft.VisualStudio.Debugger.TargetComposition.Remote", "Microsoft.VisualStudio.Debugger.TargetComposition.Remote", "Microsoft.VisualStudio.Debugger.Remote", "Microsoft.VisualStudio.Debugger.Concord.Remote", "Microsoft.VisualStudio.Debugger.Concord.Remote.Resources", "Microsoft.VisualStudio.Debugger.Remote", "Microsoft.VisualStudio.Debugger.Remote.ARM64", "Microsoft.VisualStudio.Debugger.Concord.Remote.ARM64", "Microsoft.VisualStudio.Debugger.Concord.Remote.Resources.ARM64", "Microsoft.VisualStudio.Debugger.Remote.ARM", "Microsoft.VisualStudio.Debugger.Concord.Remote.ARM", "Microsoft.VisualStudio.Debugger.Concord.Remote.Resources.ARM", "Microsoft.VisualStudio.Debugger.Remote.Resources.ARM", "Microsoft.VisualStudio.Debugger.Remote.Resources.ARM64", "Microsoft.VisualStudio.Debugger.Concord.Remote", "Microsoft.VisualStudio.Debugger.Concord.Remote.Resources", "Microsoft.VisualStudio.Debugger.Remote.Resources", "Microsoft.VisualStudio.Debugger.Remote.Resources", "Microsoft.VisualStudio.Debugger", "Microsoft.VisualStudio.VC.MSVCDis", "Microsoft.IntelliTrace.DiagnosticsHub", "Microsoft.VisualStudio.Debugger.Concord", "Microsoft.VisualStudio.Debugger.Concord.Resources", "Microsoft.VisualStudio.Debugger.Resources", "Microsoft.VisualStudio.Debugger.Package.DiagHub.Client", "Microsoft.VisualStudio.Debugger.Remote.DiagnosticsHub.Client", "Microsoft.VisualStudio.Debugger.Remote.DiagnosticsHub.Client", "Microsoft.VisualStudio.Debugger.Remote.DiagnosticsHub.Client", "Microsoft.PackageGroup.ClientDiagnostics", "Microsoft.VisualStudio.AppResponsiveness", "Microsoft.VisualStudio.AppResponsiveness.Targeted", "Microsoft.VisualStudio.AppResponsiveness.Resources", "Microsoft.VisualStudio.ClientDiagnostics", "Microsoft.VisualStudio.ClientDiagnostics.Targeted", "Microsoft.VisualStudio.ClientDiagnostics.Resources", "Microsoft.VisualStudio.PackageGroup.CommunityCore", "Microsoft.VisualStudio.ProjectSystem.Full", "Microsoft.VisualStudio.LiveShareApi", "Microsoft.VisualStudio.ProjectSystem.Query", "Microsoft.VisualStudio.ProjectSystem", "Microsoft.VisualStudio.Community.x86", "Microsoft.VisualStudio.Community.x64", "Microsoft.VisualStudio.Community.Msi.Resources", "Microsoft.VisualStudio.Community.Msi", "Microsoft.VisualStudio.Community.Shared.Msi", "Microsoft.VisualStudio.Devenv.Msi", "Microsoft.VisualStudio.Devenv.Shared.Msi", "Microsoft.VisualStudio.MinShell.Interop.Msi", "Microsoft.VisualStudio.MinShell.Interop.Shared.Msi", "Microsoft.VisualStudio.Editors", "Microsoft.VisualStudio.Workload.CoreEditor", "Microsoft.VisualStudio.Component.CoreEditor", "Microsoft.VisualStudio.PackageGroup.CoreEditor", "Microsoft.WebView2", "Microsoft.VisualStudio.ScriptedHost", "Microsoft.VisualStudio.ScriptedHost.Targeted", "Microsoft.VisualCpp.Tools.Common.UtilsPrereq", "Microsoft.VisualCpp.Tools.Common.Utils", "Microsoft.VisualCpp.Tools.Common.Utils.Resources", "Microsoft.VisualStudio.PackageGroup.VsDevCmd", "Microsoft.VisualStudio.VsDevCmd.Ext.NetFxSdk", "Microsoft.VisualStudio.VsDevCmd.Core.WinSdk", "Microsoft.VisualStudio.VsDevCmd.Core.DotNet", "Microsoft.VisualStudio.VC.DevCmd", "Microsoft.VisualStudio.VC.DevCmd.Resources", "Microsoft.VisualStudio.VirtualTree", "Microsoft.DiaSymReader", "Microsoft.Build.Dependencies", "Microsoft.Build.FileTracker.Msi", "Microsoft.Build", "Microsoft.VisualStudio.PackageGroup.NuGet", "Microsoft.DataAI.NuGetRecommender", "Microsoft.VisualStudio.NuGet.Core", "Microsoft.Build.Arm64", "Microsoft.Build.UnGAC", "Microsoft.VisualStudio.TextMateGrammars", "Microsoft.VisualStudio.Platform.Markdown", "Microsoft.VisualStudio.Platform.CrossRepositorySearch", "Microsoft.VisualStudio.PackageGroup.TeamExplorer.Common", "Microsoft.VisualStudio.TeamExplorer", "Microsoft.VisualStudio.PackageGroup.ServiceHub", "Microsoft.ServiceHub.Node", "Microsoft.ServiceHub.Managed", "Microsoft.ServiceHub.arm64", "Microsoft.VisualStudio.ProjectServices", "Microsoft.VisualStudio.OpenFolder.VSIX", "Microsoft.VisualStudio.FileHandler.Msi", "Microsoft.VisualStudio.FileHandler.Msi", "Microsoft.VisualStudio.PackageGroup.MinShell", "Microsoft.VisualStudio.MinShell.Msi", "Microsoft.VisualStudio.MinShell.Shared.Msi", "Microsoft.VisualStudio.MinShell.Msi.Resources", "Microsoft.VisualStudio.MinShell.Interop", "CoreEditorFonts", "Microsoft.VisualStudio.Log", "Microsoft.VisualStudio.Log.Targeted", "Microsoft.VisualStudio.Log.Resources", "Microsoft.VisualStudio.Finalizer", "Microsoft.VisualStudio.Devenv", "Microsoft.VisualStudio.Devenv.Resources", "Microsoft.VisualStudio.CoreEditor", "Microsoft.VisualStudio.Navigation.RichCodeNav", "Microsoft.VisualStudio.Platform.NavigateTo", "Microsoft.VisualStudio.Connected", "SQLitePCLRaw", "SQLitePCLRaw.Targeted", "Microsoft.VisualStudio.Connected.Auto", "Microsoft.VisualStudio.Connected.Auto.Resources", "Microsoft.VisualStudio.AzureSDK", "Microsoft.VisualStudio.PerfLib", "Microsoft.VisualStudio.Connected.Resources", "Microsoft.Net.PackageGroup.4.8.Redist", "Microsoft.VisualStudio.PackageGroup.Progression", "Microsoft.VisualStudio.PerformanceProvider", "Microsoft.VisualStudio.GraphModel", "Microsoft.VisualStudio.GraphProvider", "Microsoft.VisualStudio.Community.VB.Targeted", "Microsoft.VisualStudio.Community.VB.Neutral", "Microsoft.VisualStudio.Community.CSharp.Targeted", "Microsoft.VisualStudio.Community.CSharp.Neutral", "Microsoft.VisualStudio.Community.ProductArch.TargetedExtra", "Microsoft.VisualStudio.Community.ProductArch.Targeted", "Microsoft.VisualStudio.Community.ProductArch.NeutralExtra", "Microsoft.DiaSymReader.PortablePdb", "Microsoft.IntelliTrace.CollectorCab", "Microsoft.VisualStudio.Community.VB.Resources.Targeted", "Microsoft.VisualStudio.Community.VB.Resources.Neutral", "Microsoft.VisualStudio.Community.CSharp.Resources.Targeted", "Microsoft.VisualStudio.Community.CSharp.Resources.Neutral", "Microsoft.VisualStudio.Community.ProductArch.Resources.Targeted", "Microsoft.VisualStudio.Community.ProductArch.Resources.NeutralExtra", "Microsoft.VisualStudio.Net.Eula.Resources", "Microsoft.VisualStudio.Community.ProductArch.Resources.Neutral", "Microsoft.VisualStudio.WebSiteProject.DTE", "Microsoft.VisualStudio.Diagnostics.AspNetHelper", "Microsoft.VisualStudio.Diagnostics.AspNetHelper.Standard", "Microsoft.MSHtml", "Microsoft.VisualStudio.Platform.CallHierarchy", "Microsoft.VisualStudio.Community.ProductArch.Neutral", "Microsoft.VisualStudio.MinShell", "Microsoft.VisualStudio.VsWebProtocolSelector.Msi", "Microsoft.Net.6.WindowsDesktop.Runtime", "Microsoft.Net.6.Runtime", "Microsoft.VisualStudio.PackageGroup.Setup.Common", "Microsoft.VisualStudio.Setup.WMIProvider", "Microsoft.VisualStudio.Setup.Configuration.Interop", "Microsoft.VisualStudio.Setup.Configuration", "Microsoft.VisualStudio.Extensibility.Container", "Microsoft.VisualStudio.LanguageServer", "Microsoft.VisualStudio.Platform.Terminal", "Microsoft.VisualStudio.MefHosting", "Microsoft.VisualStudio.Initializer", "Microsoft.VisualStudio.ExtensionManager", "Microsoft.VisualStudio.Platform.Editor", "Microsoft.VisualStudio.MinShell.Targeted", "Microsoft.VisualStudio.NativeImageSupport", "Microsoft.VisualStudio.Devenv.Config", "Microsoft.VisualStudio.MinShell.Resources.arm64", "Microsoft.VisualStudio.MinShell.Auto", "Microsoft.VisualStudio.MinShell.Auto.Resources", "Microsoft.VisualStudio.Branding.Community"]}]