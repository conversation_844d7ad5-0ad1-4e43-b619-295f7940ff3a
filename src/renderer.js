const {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  Container,
  Box,
  CssBaseline,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Stack,
  Autocomplete
} = MaterialUI;

const MenuIcon = MaterialUI.icons?.Menu || (() => <span>☰</span>);
const AddIcon = MaterialUI.icons?.Add || (() => <span>＋</span>);

function getAllTags(tasks) {
  // Estrae tutti i tag unici dai task
  const tags = new Set();
  tasks.forEach(t => (t.tags || []).forEach(tag => tags.add(tag)));
  return Array.from(tags);
}

function App() {
  const [tasks, setTasks] = React.useState([]);
  const [filterTag, setFilterTag] = React.useState(null);
  const [description, setDescription] = React.useState('');
  const [tagsInput, setTagsInput] = React.useState('');
  const [deadline, setDeadline] = React.useState('');

  const tags = Tasks.uniqueTags(tasks);
  const displayed = filterTag ? Tasks.filterTasksByTag(tasks, filterTag) : tasks;

  function onSubmit(ev) {
    ev.preventDefault();
    if (!description.trim()) return;
    const tags = tagsInput.split(',').map(t => t.trim()).filter(Boolean);
    const task = Tasks.createTask(description.trim(), tags, deadline || null);
    setTasks(prev => [...prev, task]);
    setDescription('');
    setTagsInput('');
    setDeadline('');
  }

  return e('div', { className: 'container' },
    e('div', { className: 'sidebar' },
      e('h3', null, 'Tags'),
      e('ul', null,
        e('li', { key: 'inbox' },
          e('button', { onClick: () => setFilterTag(null) }, 'Inbox')
        ),
        tags.map(tag =>
          e('li', { key: tag },
            e('button', { onClick: () => setFilterTag(tag) }, tag)
          )
        )
      )
    ),
    e('div', { className: 'main' },
      e('h1', null, 'Tasks'),
      e('form', { onSubmit },
        e('input', {
          placeholder: 'Description',
          value: description,
          onChange: ev => setDescription(ev.target.value)
        }),
        e('input', {
          placeholder: 'Tags (comma separated)',
          value: tagsInput,
          onChange: ev => setTagsInput(ev.target.value)
        }),
        e('input', {
          type: 'date',
          value: deadline,
          onChange: ev => setDeadline(ev.target.value)
        }),
        e('button', { type: 'submit' }, 'Add')
      ),
      e('ul', null,
        displayed.map(task =>
          e('li', { key: task.id },
            e('span', null, task.description),
            task.deadline ? e('span', null, ` (Due: ${task.deadline})`) : null,
            task.tags.length ? e('span', null, ` [${task.tags.join(', ')}]`) : null
          )
        )
      )
    )
  );
}

const domContainer = document.getElementById('root');
const root = ReactDOM.createRoot(domContainer);
root.render(<App />);
