<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Red Tasks</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <!-- Material-UI CSS -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
</head>
<body>
  <div id="root"></div>

  <!-- React -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

  <!-- Material-UI -->
  <script src="https://unpkg.com/@mui/material@latest/umd/material-ui.development.js"></script>

  <!-- Babel for JSX transformation -->
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

  <!-- Our modules -->
  <script src="../src/tasks.js"></script>
  <script type="text/babel" src="../src/renderer.js"></script>
</body>
</html>
