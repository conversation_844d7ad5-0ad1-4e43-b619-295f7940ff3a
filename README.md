# red-tasks

A minimal Electron + React boilerplate.

React is loaded from a CDN so no build step is required.

## Setup

Install dependencies (requires network access):

```bash
npm install
```

## Development

Run the app in development mode:

```bash
npm run dev
```

## Start

Launch the packaged app:

```bash
npm start
```

## Tests

Verify that the dev script matches the start script:

```bash
npm test
```
