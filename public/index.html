<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Red Tasks</title>
  <style>
    body { font-family: sans-serif; margin: 0; display: flex; height: 100vh; }
    .sidebar { width: 150px; background: #f0f0f0; padding: 1em; overflow-y: auto; }
    .main { flex: 1; padding: 1em; overflow-y: auto; }
    form input { margin-right: 4px; }
    button { cursor: pointer; }
  </style>
</head>
<body>
  <div id="root"></div>
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="../src/tasks.js"></script>
  <script src="../src/renderer.js"></script>
</body>
</html>
