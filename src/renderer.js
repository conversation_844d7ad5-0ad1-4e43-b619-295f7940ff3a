const {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  Container,
  Box,
  CssBaseline,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Card,
  CardContent,
  Chip,
  Stack,
  Paper
} = MaterialUI;

// Icone Material-UI
const MenuIcon = () => <span className="material-icons">menu</span>;
const AddIcon = () => <span className="material-icons">add</span>;
const InboxIcon = () => <span className="material-icons">inbox</span>;
const LabelIcon = () => <span className="material-icons">label</span>;

function App() {
  const [tasks, setTasks] = React.useState([]);
  const [filterTag, setFilterTag] = React.useState(null);
  const [description, setDescription] = React.useState('');
  const [tagsInput, setTagsInput] = React.useState('');
  const [deadline, setDeadline] = React.useState('');
  const [drawerOpen, setDrawerOpen] = React.useState(false);

  const tags = Tasks.uniqueTags(tasks);
  const displayed = filterTag ? Tasks.filterTasksByTag(tasks, filterTag) : tasks;

  function onSubmit(ev) {
    ev.preventDefault();
    if (!description.trim()) return;
    const taskTags = tagsInput.split(',').map(t => t.trim()).filter(Boolean);
    const task = Tasks.createTask(description.trim(), taskTags, deadline || null);
    setTasks(prev => [...prev, task]);
    setDescription('');
    setTagsInput('');
    setDeadline('');
  }

  const drawerWidth = 240;

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={() => setDrawerOpen(!drawerOpen)}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            Red Tasks
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Sidebar Drawer */}
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto', p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Filtri
          </Typography>

          <List>
            <ListItemButton
              selected={filterTag === null}
              onClick={() => setFilterTag(null)}
            >
              <InboxIcon />
              <ListItemText primary="Inbox" sx={{ ml: 1 }} />
            </ListItemButton>

            <Divider sx={{ my: 1 }} />

            {tags.map(tag => (
              <ListItemButton
                key={tag}
                selected={filterTag === tag}
                onClick={() => setFilterTag(tag)}
              >
                <LabelIcon />
                <ListItemText primary={tag} sx={{ ml: 1 }} />
              </ListItemButton>
            ))}
          </List>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />

        <Container maxWidth="md">
          <Typography variant="h4" gutterBottom>
            {filterTag ? `Tasks: ${filterTag}` : 'Tutte le Tasks'}
          </Typography>

          {/* Add Task Form */}
          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Aggiungi nuova task
            </Typography>

            <Box component="form" onSubmit={onSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Descrizione"
                variant="outlined"
                fullWidth
                value={description}
                onChange={(ev) => setDescription(ev.target.value)}
                required
              />

              <TextField
                label="Tags (separati da virgola)"
                variant="outlined"
                fullWidth
                value={tagsInput}
                onChange={(ev) => setTagsInput(ev.target.value)}
                helperText="Es: lavoro, urgente, progetto"
              />

              <TextField
                label="Scadenza"
                type="date"
                variant="outlined"
                value={deadline}
                onChange={(ev) => setDeadline(ev.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
              />

              <Button
                type="submit"
                variant="contained"
                startIcon={<AddIcon />}
                sx={{ alignSelf: 'flex-start' }}
              >
                Aggiungi Task
              </Button>
            </Box>
          </Paper>

          {/* Tasks List */}
          <Stack spacing={2}>
            {displayed.length === 0 ? (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                {filterTag ? `Nessuna task con tag "${filterTag}"` : 'Nessuna task presente'}
              </Typography>
            ) : (
              displayed.map(task => (
                <Card key={task.id} variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {task.description}
                    </Typography>

                    {task.deadline && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Scadenza: {new Date(task.deadline).toLocaleDateString('it-IT')}
                      </Typography>
                    )}

                    {task.tags.length > 0 && (
                      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                        {task.tags.map(tag => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            onClick={() => setFilterTag(tag)}
                            sx={{ cursor: 'pointer' }}
                          />
                        ))}
                      </Stack>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </Stack>
        </Container>
      </Box>
    </Box>
  );
}

// Render dell'app
const domContainer = document.getElementById('root');
const root = ReactDOM.createRoot(domContainer);
root.render(<App />);
