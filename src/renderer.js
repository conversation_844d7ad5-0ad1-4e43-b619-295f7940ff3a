const {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Container,
  Box,
  CssBaseline,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Stack,
  Autocomplete
} = MaterialUI;

const MenuIcon = MaterialUI.icons?.Menu || (() => <span>☰</span>);
const AddIcon = MaterialUI.icons?.Add || (() => <span>＋</span>);

function getAllTags(tasks) {
  // Estrae tutti i tag unici dai task
  const tags = new Set();
  tasks.forEach(t => (t.tags || []).forEach(tag => tags.add(tag)));
  return Array.from(tags);
}

function App() {
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [tasks, setTasks] = React.useState([]);

  // Carica i task dal DB all'avvio
  React.useEffect(() => {
    window.api.getTasks().then(setTasks);
  }, []);

  const [selectedTag, setSelectedTag] = React.useState(null); // Tag selezionato per filtro
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [newTask, setNewTask] = React.useState({
    name: '',
    date: '',
    type: 'event',
    tags: [],
  });

  // Sidebar
  const handleDrawerClose = () => setDrawerOpen(false);
  const handleDrawerOpen = () => setDrawerOpen(true);

  // Dialog
  const handleDialogOpen = () => setDialogOpen(true);
  const handleDialogClose = () => {
    setDialogOpen(false);
    setNewTask({ name: '', date: '', type: 'event', tags: [] });
  };
  const handleNewTaskChange = (field) => (event, value) => {
    setNewTask((prev) => ({
      ...prev,
      [field]: value !== undefined ? value : event.target.value,
    }));
  };
  const handleTaskTypeChange = (event, newType) => {
    if (newType !== null) setNewTask((prev) => ({ ...prev, type: newType }));
  };
  const handleTagsChange = (event, value) => {
    setNewTask((prev) => ({ ...prev, tags: value }));
  };
  const handleTaskAdd = async () => {
    await window.api.addTask(newTask);
    const updated = await window.api.getTasks();
    setTasks(updated);
    handleDialogClose();
  };


  // Filtra i task per tag selezionato
  const filteredTasks = selectedTag
    ? tasks.filter(task => (task.tags || []).includes(selectedTag))
    : tasks;

  const allTags = getAllTags(tasks);

  return (
    <React.Fragment>
      <CssBaseline />
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            edge="start"
            onClick={() => setDrawerOpen((prev) => !prev)}
            sx={{ mr: 2 }}
            aria-label="toggle sidebar"
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Red Tasks
          </Typography>
        </Toolbar>
      </AppBar>
      <Drawer
        variant="persistent"
        anchor="left"
        open={drawerOpen}
        sx={{
          width: 220,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 220,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar>
          <IconButton onClick={handleDrawerClose} aria-label="close sidebar">
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" sx={{ ml: 1 }}>
            Tag
          </Typography>
        </Toolbar>
        <Divider />
        <List>
          {allTags.length === 0 && (
            <ListItem>
              <ListItemText primary="No tags" />
            </ListItem>
          )}
          {allTags.map((tag) => (
            <ListItem key={tag} disablePadding>
              <ListItemButton selected={selectedTag === tag} onClick={() => setSelectedTag(selectedTag === tag ? null : tag)}>
                <ListItemText primary={tag} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>
      <Toolbar /> {/* Offset for AppBar */}
      <Container
        sx={{
          mt: 4,
          minHeight: '80vh',
          display: 'flex',
          alignItems: 'flex-start',
          justifyContent: 'center',
          position: 'relative',
          width: '100vw',
          maxWidth: '100vw',
          overflow: 'hidden',
          transition: 'margin-left 0.3s, transform 0.3s',
          marginLeft: drawerOpen ? { xs: 0, sm: '220px' } : 0,
          transform: drawerOpen ? 'scale(0.98)' : 'scale(1)',
          boxSizing: 'border-box',
        }}
      >
        <Box sx={{ width: '100%', maxWidth: 600 }}>
          <Typography variant="h4" gutterBottom>
            Inbox
          </Typography>
          {filteredTasks.length === 0 ? (
            <Typography variant="body1" color="text.secondary">Nessun task presente.</Typography>
          ) : (
            <List>
              {filteredTasks.map((task) => (
                <ListItem key={task.id} sx={{ borderRadius: 2, mb: 1, boxShadow: 1 }}>
                  <Box sx={{ width: '100%' }}>
                    <Typography variant="h6">{task.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {task.type === 'event' ? 'Event' : 'Deadline'} - {task.date}
                    </Typography>
                    {task.tags && task.tags.length > 0 && (
                      <Box sx={{ mt: 0.5 }}>
                        {task.tags.map(tag => (
                          <Box key={tag} component="span" sx={{ display: 'inline-block', bgcolor: '#eee', px: 1, py: 0.2, borderRadius: 1, mr: 0.5, fontSize: 12 }}>
                            {tag}
                          </Box>
                        ))}
                      </Box>
                    )}
                  </Box>
                </ListItem>
              ))}
            </List>
          )}
        </Box>
        <Fab
          color="primary"
          aria-label="add"
          sx={{ position: 'fixed', bottom: 32, right: 32 }}
          onClick={handleDialogOpen}
        >
          <AddIcon />
        </Fab>
        <Dialog open={dialogOpen} onClose={handleDialogClose}>
          <DialogTitle>Add a new task</DialogTitle>
          <DialogContent>
            <Stack spacing={2} sx={{ mt: 1 }}>
              <TextField
                label="Task Name"
                value={newTask.name}
                onChange={handleNewTaskChange('name')}
                autoFocus
                fullWidth
              />
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <Button
                  variant={newTask.date === 'ASAP' ? 'contained' : 'outlined'}
                  color="secondary"
                  onClick={() => setNewTask(prev => ({ ...prev, date: 'ASAP' }))}
                >
                  ASAP
                </Button>
                <Button
                  variant={newTask.date === 'Anytime' ? 'contained' : 'outlined'}
                  color="secondary"
                  onClick={() => setNewTask(prev => ({ ...prev, date: 'Anytime' }))}
                >
                  Anytime
                </Button>
              </Box>
              <TextField
                label="Date"
                type="date"
                value={newTask.date === 'ASAP' || newTask.date === 'Anytime' ? '' : newTask.date}
                onChange={handleNewTaskChange('date')}
                InputLabelProps={{ shrink: true }}
                fullWidth
                disabled={newTask.date === 'ASAP' || newTask.date === 'Anytime'}
              />
              <ToggleButtonGroup
                value={newTask.type}
                exclusive
                onChange={handleTaskTypeChange}
                aria-label="task type"
                fullWidth
              >
                <ToggleButton value="event" aria-label="event">
                  Event
                </ToggleButton>
                <ToggleButton value="deadline" aria-label="deadline">
                  Deadline
                </ToggleButton>
              </ToggleButtonGroup>
              <Autocomplete
                multiple
                freeSolo
                options={allTags}
                value={newTask.tags}
                onChange={handleTagsChange}
                renderInput={(params) => <TextField {...params} label="Tags" placeholder="Aggiungi tag" />}
              />
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose}>Cancel</Button>
            <Button onClick={handleTaskAdd} variant="contained" disabled={!newTask.name || !newTask.date}>
              Add Task
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </React.Fragment>
  );
}

const domContainer = document.getElementById('root');
const root = ReactDOM.createRoot(domContainer);
root.render(<App />);
